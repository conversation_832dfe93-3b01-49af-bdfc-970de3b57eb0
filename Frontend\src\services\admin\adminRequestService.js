import axios from 'axios';
import { adminServiceUtils } from './index';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with common config
const api = axios.create({
  baseURL: `${API_BASE_URL}/admin/requests`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle responses and errors
api.interceptors.response.use(
  (response) => adminServiceUtils.handleApiSuccess(response),
  (error) => adminServiceUtils.handleApiError(error)
);

const adminRequestService = {
  // Get all requests with filtering, sorting, and pagination
  getAllRequests: async (params = {}) => {
    try {
      const queryParams = adminServiceUtils.getPaginationParams(
        params.page,
        params.limit,
        params.search,
        params.sortBy,
        params.sortOrder
      );

      // Add additional filters
      if (params.status) queryParams.status = params.status;
      if (params.contentType) queryParams.contentType = params.contentType;
      if (params.sport) queryParams.sport = params.sport;
      if (params.dateFrom) queryParams.dateFrom = params.dateFrom;
      if (params.dateTo) queryParams.dateTo = params.dateTo;
      if (params.minBudget) queryParams.minBudget = params.minBudget;
      if (params.maxBudget) queryParams.maxBudget = params.maxBudget;
      if (params.buyerId) queryParams.buyerId = params.buyerId;
      if (params.sellerId) queryParams.sellerId = params.sellerId;

      const response = await api.get('/', { params: queryParams });
      return response;
    } catch (error) {
      console.error('Error fetching requests:', error);
      throw error;
    }
  },

  // Get request by ID
  getRequestById: async (id) => {
    try {
      const response = await api.get(`/${id}`);
      return response;
    } catch (error) {
      console.error('Error fetching request:', error);
      throw error;
    }
  },

  // Update request status
  updateRequestStatus: async (id, data) => {
    try {
      const response = await api.put(`/${id}/status`, data);
      return response;
    } catch (error) {
      console.error('Error updating request status:', error);
      throw error;
    }
  },

  // Delete request
  deleteRequest: async (id) => {
    try {
      const response = await api.delete(`/${id}`);
      return response;
    } catch (error) {
      console.error('Error deleting request:', error);
      throw error;
    }
  },

  // Bulk update requests
  bulkUpdateRequests: async (data) => {
    try {
      const response = await api.post('/bulk-update', data);
      return response;
    } catch (error) {
      console.error('Error bulk updating requests:', error);
      throw error;
    }
  },

  // Bulk delete requests
  bulkDeleteRequests: async (requestIds) => {
    try {
      const response = await api.post('/bulk-delete', { requestIds });
      return response;
    } catch (error) {
      console.error('Error bulk deleting requests:', error);
      throw error;
    }
  },

  // Get request statistics
  getRequestStats: async () => {
    try {
      const response = await api.get('/stats');
      return response;
    } catch (error) {
      console.error('Error fetching request stats:', error);
      throw error;
    }
  },

  // Export requests
  exportRequests: async (params = {}) => {
    try {
      const response = await api.get('/export', { 
        params,
        responseType: 'blob'
      });
      return response;
    } catch (error) {
      console.error('Error exporting requests:', error);
      throw error;
    }
  },

  // Get request analytics
  getRequestAnalytics: async (params = {}) => {
    try {
      const response = await api.get('/analytics', { params });
      return response;
    } catch (error) {
      console.error('Error fetching request analytics:', error);
      throw error;
    }
  },

  // Flag request
  flagRequest: async (id, reason) => {
    try {
      const response = await api.put(`/${id}/flag`, { reason });
      return response;
    } catch (error) {
      console.error('Error flagging request:', error);
      throw error;
    }
  },

  // Unflag request
  unflagRequest: async (id) => {
    try {
      const response = await api.put(`/${id}/unflag`);
      return response;
    } catch (error) {
      console.error('Error unflagging request:', error);
      throw error;
    }
  },

  // Moderate request
  moderateRequest: async (id, data) => {
    try {
      const response = await api.put(`/${id}/moderate`, data);
      return response;
    } catch (error) {
      console.error('Error moderating request:', error);
      throw error;
    }
  },

  // Get request timeline
  getRequestTimeline: async (id) => {
    try {
      const response = await api.get(`/${id}/timeline`);
      return response;
    } catch (error) {
      console.error('Error fetching request timeline:', error);
      throw error;
    }
  }
};

export default adminRequestService;
